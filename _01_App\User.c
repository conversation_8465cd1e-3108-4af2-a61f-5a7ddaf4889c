/* ****************************
 * Project description:
 *
 * A Project empty template file
 *
 * Author: 创新基地 -> 2023 Bo
 *
 * Creation Date: 2023/04/07
 *
 * Update date: 2023/04/07
 * ****************************/

/* ***************************** Include & Define Part     	*****************************
 * 头文件声明及宏定义区
 * */
#include "User.h"

/* ***************************** Variable definition Part   *****************************
 * 变量定义区
 * */

// 宏定义


#define Z_DESIRED 10.0
			// 定义滞后因子和切换阈值

#define windowsize  10 //均值滤波的动态窗口大小
#define steeping 10 //步进的次数
#define average_val 5//显示的平均

#define Pi 3.1415926535
//u8 DDS_Switch=0;


// ADC采样序列
extern uint32_t ADCData[];
// 全局变量
static uint8_t gear_change_counter = 0;
// 菜单序号变量
uint8_t MenuSign = 0;

uint8_t menu_flag;
/* ***************************** Main Part                  *****************************
 * 主函数区
 * */
void User_main(void)
{ 
	uint16_t count = 0;
	
	// 初始化全部
	Init_All();
	
	// 显示主界面
	//Disp_Main();
	My_Disp_Main();

	while(1)
	{
//		dds[0].fre = 200000;
//		sendData(dds[0],0);
		switch( MenuSign )
		{
			case 0: 
				if( Ps2KeyValue != KeyValue_Null ) 	//在未选中菜单时有按键按下
					Change_Menu( Ps2KeyValue );				//根据按键改变菜单界面
				  	menu_show();
				;break;
			case 1: MenuHaddler_1(); break;
			case 2: MenuHaddler_2(); break;
			case 3: MenuHaddler_3(); break;
			case 4: MenuHaddler_4(); break;
			default: break;
		}
		
		delay_ms(10);
	}
}

void GPIO_Config(void)
{
  GPIO_InitTypeDef GPIO_InitStructure;
  
  // 使能GPIOC时钟
  RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
  
  // 配置PC10、PC11、PC12引脚
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_12;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;       // 设置为输出模式，根据需要可修改
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;   // 设置速度
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;      // 推挽输出
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;    // 无上下拉
  
  // 初始化GPIOC
  GPIO_Init(GPIOC, &GPIO_InitStructure);
}


/* ***************************** Initialization Part        *****************************
 * 初始化函数区
 * */

// 初始化全部 参数：无
void Init_All()
{
	LCD_Clear(Black);
	
	// 初始化ADC
	User_ADC_Init( 0 );//单通道采集
	
	// 初始化DAC
	User_DAC_Init();
	
	GPIO_Config();
	// 初始化AD8370
	//AD8370_Init();
	
	// 初始化DAC8562
	//DAC8562_Init( 0 );
	
	// 初始化PGA2310
	PGA2310_Init();
	
	// 初始化IIC通信
	//IIC_Init();
	
	// 初始化SPI通信
	//SPI_GPIO_Init( 1 );
	
	// 初始化串口(用于控制9958和9851输出)
	Init_Uart(115200);
	// DDS结构体初始化
	DDSDataInit();
	
	//Init_Uart_6(115200);
	// 初始化ADS1256
	//ADS1256_Init();

	IIR_Init();

}

/* ***************************** Display Part               *****************************
 * 显示函数区
 * */

// 显示界面框架 参数：无
void Disp_Main()
{
	uint8_t count;
	
	// Show title
	OS_String_Show( 400 - 32 * 4 , 16 ,32 , 0 , TitleStr );
	
	// Draw line
	LCD_Appoint_Clear( 0 ,64 , 800 , 64 + 8 ,White );
	LCD_Appoint_Clear( 0 ,480 - 32 - 8 , 800 , 480 - 32 ,White );
	LCD_Appoint_Clear( 250 , 64 + 8 , 250 + 2 , 480 - 32 - 8 , White );
	//LCD_Appoint_Clear( 300 + 2 , 96 + 64 * 4 , 800 , 96 + 64 * 4 + 2 , White );
	
	// Show model version str
	OS_String_Show( 32 , 480 - 16 - 8 ,16 , 0 , ModelVerStr );
	// Show user version str
	OS_String_Show( 632 , 480 - 16 - 8 ,16 , 0 , UserVerStr );
	
	// Disp menu
	for( count = 1 ; count < MenuChoiceNum + 1 ; count ++ )
		OS_String_Show( 32 , 32 + 64 * count ,32 , 0 , "—" );
	for( count = 0 ; count < MenuChoiceNum ; count ++ )
	{
		switch( count )
		{
			case 0: OS_String_Show( 80 , 96 ,32 , 0 , Menu1Choice1 );break;
			case 1: OS_String_Show( 80 , 96 + 64 ,32 , 0 , Menu1Choice2 );break;
			case 2: OS_String_Show( 80 , 96 + 64 * 2 ,32 , 0 , Menu1Choice3 );break;
			case 3: OS_String_Show( 80 , 96 + 64 * 3 ,32 , 0 , Menu1Choice4 );break;
			default: break;
		}
	}
	
//	switch (DDS_Switch)
//	{
//		case 0:
//			OS_String_Show(350,96+64,32,1,"关");
//			OS_String_Show(350+32*7,96+64+16,16,1,"按0，1开关信号源");
//		  break;
//		case 1:
//			OS_String_Show(350,96+64,32,1,"开");
//			OS_String_Show(350+32*7,96+64+16,16,1,"按0，1开关信号源");
//			break;
//	}
}

void  My_Disp_Main()
{
	uint8_t count;
	OS_String_Show( 400 - 32 * 4 , 16 ,32 , 0 , TitleStr );

	for( count = 0 ; count < MenuChoiceNum ; count ++ )
	{
		switch( count )
		{
			case 0: 
				OS_String_Show( 320 , 96 ,32 , 0 , Menu1Choice1 );
				LCD_DrawRect(315,91,32+15,32*6);
			break;
			case 1: 
				OS_String_Show( 320 , 96 + 64 ,32 , 0 , Menu1Choice2 );
				LCD_DrawRect(315,91+64,32+15,32*6);
			break;
			case 2: 
				OS_String_Show( 320 , 96 + 64 * 2 ,32 , 0 , Menu1Choice3 );
				LCD_DrawRect(315,91+64*2,32+15,32*6);
			break;
			case 3: 
				OS_String_Show( 320 , 96 + 64 * 3 ,32 , 0 , Menu1Choice4 );
				LCD_DrawRect(315,91+64*3,32+15,32*6);
			break;
			default: break;
		}
	}
	menu_flag = 0;
}

void menu_show()
{
	uint8_t count;
	//OS_String_Show( 400 - 32 * 4 , 16 ,32 , 0 , TitleStr );
	if(menu_flag == 0)
	{
		My_Disp_Main();
		return;
	}
	LCD_Appoint_Clear( 0 ,64 , 800 , 64 + 8 ,White );
	LCD_Appoint_Clear( 245 , 0 , 250 , 64 , White );
	// for( count = 0 ; count < MenuChoiceNum ; count ++ )
	// {
	// 	switch( count )
	// 	{
	// 		case 0: OS_String_Show( 80 , 96 ,32 , 0 , Menu1Choice1 );break;
	// 		case 1: OS_String_Show( 80 , 96 + 64 ,32 , 0 , Menu1Choice2 );break;
	// 		case 2: OS_String_Show( 80 , 96 + 64 * 2 ,32 , 0 , Menu1Choice3 );break;
	// 		case 3: OS_String_Show( 80 , 96 + 64 * 3 ,32 , 0 , Menu1Choice4 );break;
	// 		default: break;
	// 	}
	// }
}

// 输出数值 参数：1>位置；2>数值；3>输出格式,如"数值%0.0f"
void Show_Val( uint8_t location , float value , char *str )
{
	
	if( location > 0 && location <= 10 )
		OS_Num_Show(  64 , 96 + 32 * ( location - 1 ) ,32 , 1 , value , str );
	else if( location > 10 && location <= 20 )
		OS_Num_Show( 400 + 64 , 96 + 32 * ( location - 11 ) ,32 , 1 , value , str );
	else
		OS_String_Show( 250 + 64 , 96 ,32 , 1 , "ERROR" );
	
}

// 切换菜单页 参数：1>菜单序号(1~5)
void Change_Menu( uint8_t menu_sign )
{
	uint8_t count;
	
	// 清除显示区域
	LCD_Appoint_Clear( 0  , 0 , 800 , 480 , Black );
	
	// for( count = 1 ; count < MenuChoiceNum + 1 ; count ++ )
	// 	OS_String_Show( 32 , 32 + 64 * count ,32 , 1 , "—" );
	
	if( menu_sign > 0 && menu_sign <= MenuChoiceNum )
	{
		OS_String_Show( 32 , 25 ,32 , 1 , "->" );
		switch( menu_sign )
		{
			case 1: OS_String_Show( 80 , 25 ,32 , 0 , Menu1Choice1 );break;
			case 2: OS_String_Show( 80 , 25 ,32 , 0 , Menu1Choice2 );break;
			case 3: OS_String_Show( 80 , 25 ,32 , 0 , Menu1Choice3 );break;
			case 4: OS_String_Show( 80 , 25 ,32 , 0 , Menu1Choice4 );break;
			default: break;
		}
		menu_flag = 1;
	}
	else
	{
		menu_sign = 0;
		menu_flag = 0;
	}
		
	
	Ps2KeyValue = KeyValue_Null;
	MenuSign = menu_sign;
}


float linear(uint32_t input_data,uint32_t low_lever,uint32_t high_lever,uint32_t low_slope,uint32_t high_slope)
{
	return low_slope + (input_data-input_data)/(high_lever-low_lever)*(high_slope-low_slope);
}
/* ***************************** Menu Handler Part     	 	 	*****************************
 * 菜单执行函数区
 */

// 菜单1执行函数 参数：无
void MenuHaddler_1()
{
	uint8_t mode = 0;
	float key_value;
	float key_old;

	float A=0,B=0,LowAv=0,Vol=1;

	Ps2KeyValue = KeyValue_Null;

	
	while( Ps2KeyValue != KeyValue_Back )
	{
		switch (mode)
		{
			case 0:
				Show_Val(1,0,"1.输出频率可控信号");
				Show_Val(2,0,"2.输出信号");
				
				if(Ps2KeyValue == KeyValue_1)
				{
					mode = 1;
					Ps2KeyValue = KeyValue_Null;
					LCD_Appoint_Clear( 64  , 96  , 800 , 480 , Black );
				}		
				else if(Ps2KeyValue == KeyValue_2)
				{
					mode = 2;
					Ps2KeyValue = KeyValue_Null;
					LCD_Appoint_Clear( 64  , 96  , 800 , 480 , Black );
				}
				
			break;
			case 1:
				Show_Val(1,0,"输出频率可控信号");
				dds[1].range = 200;
				dds[1].fre = 1000;
				key_value = 1000;
				AD9959_senddata();
				DAC_SetChannel1Data( DAC_Align_12b_R , 1241);
				while(Ps2KeyValue != KeyValue_Back)
				{
					Show_Val(2,0,"按下按键1开始设置频率  ");
					Show_Val(3,0,"按下按键+进行频率步进  ");
					Show_Val(4,0,"按下按键-进行频率步进  ");
					if(Ps2KeyValue == KeyValue_1)
					{
						Ps2KeyValue = KeyValue_0;
						key_value=PS2_ReadNum(key_value);
					}
					if(Ps2KeyValue == KeyValue_Add)
					{
						Ps2KeyValue = KeyValue_0;
						key_value += 100;
					}
					if(Ps2KeyValue == KeyValue_Minus)
					{
						Ps2KeyValue = KeyValue_0;
						key_value -= 100;
					}
					
					if(key_value!=key_old)		//更新DDS
					{			 
						dds[1].fre = (uint32_t)key_value;
						AD9959_senddata();
					}
						
					//DAC_SetChannel1Data( DAC_Align_12b_R , 1200); 		//后面要改 	
					Show_Val(5,key_value,"频率为：%0.2f  ");
					
					key_old = key_value;
				}
				Ps2KeyValue = KeyValue_Null;
				mode = 0;
				LCD_Appoint_Clear( 64  , 96  , 800 , 480 , Black );			
			break;
			case 2:
				Show_Val(1,0,"输出信号");
				dds[1].range = 58;
				dds[1].fre = 1000;
				AD9959_senddata();
				DAC_SetChannel1Data( DAC_Align_12b_R , 870); 		//后面要改
				Show_Val(2,0,"按下按键1进入输出信号调控模式");
				while(Ps2KeyValue != KeyValue_1);
				Ps2KeyValue = KeyValue_Null;
				key_value = 1000;
				while(Ps2KeyValue != KeyValue_Back)
				{
					Show_Val(2,0,"按下按键1设置输出信号频率");
					Show_Val(3,0,"按下按键+和按键-实现输出信号幅值步进");
					if(Ps2KeyValue == KeyValue_1)
					{
						Ps2KeyValue = KeyValue_0;
						key_value=PS2_ReadNum(key_value);
					}

					if(key_value!=key_old)		//更新DDS
					{
						dds[1].fre = (uint32_t)key_value;

						//算出当前频率的衰减
						A=(1-0.00000001*(key_value*2.0*Pi)*(key_value*2.0*Pi))*(1-0.00000001*(key_value*2.0*Pi)*(key_value*2.0*Pi));
						B=(0.0003*key_value*2.0*Pi)*(0.0003*key_value*2.0*Pi);
						LowAv=sqrt(A+B)*1.0/5.0;
						LowAv=1.0/LowAv;

						if(dds[1].fre < 120)
							LowAv -= 0.788;
						//幅值固定调821
						LowAv*=5.2;
			
						dds[1].range = Vol*1000.0/LowAv+5;
//						if(dds[1].fre < 150)
//							dds[1].range += 5;
						
						

						AD9959_senddata();
					}

					if(Ps2KeyValue == KeyValue_Add)
					{
						Ps2KeyValue = KeyValue_Null;
						Vol+=0.1;
						dds[1].range = Vol*1000.0/LowAv+5;
						AD9959_senddata();
						Delay_ms(10);
					}
					if(Ps2KeyValue == KeyValue_Minus)
					{
						Ps2KeyValue = KeyValue_Null;
						Vol-=0.1;
						dds[1].range = Vol*1000.0/LowAv+5;
						AD9959_senddata();
						Delay_ms(10);
					}
					Show_Val(5,Vol,"幅值为：%0.2f");
					Show_Val(6,key_value,"频率为：%0.2f");
				key_old = key_value;

				}
				Ps2KeyValue = KeyValue_Null;
				mode = 0;
				LCD_Appoint_Clear( 64  , 96  , 800 , 480 , Black );
			break;
		}		
		
		Delay_ms(10);
	}
	
	Change_Menu( 0 );
}

// 菜单2执行函数 参数：无
void MenuHaddler_2()
{
	
	Ps2KeyValue = KeyValue_Null;
	dds[1].range = 100;
	AD9959_senddata();
	while( Ps2KeyValue != KeyValue_Back )
	{		
			if(Ps2KeyValue == KeyValue_Add)
					{
						Ps2KeyValue = KeyValue_Null;
						dds[1].range += 10;
						AD9959_senddata();
					}
				if(Ps2KeyValue == KeyValue_Minus)
					{
						Ps2KeyValue = KeyValue_Null;
						dds[1].range -= 10;
						AD9959_senddata();
					}
			
	}
	
	Change_Menu( 0 );
}


// 菜单3执行函数 参数：无
void MenuHaddler_3()
{

	//DAC_setval(2);
	Set_SamplingFre(20*20000);
	//Set_SamplingFre(20000);
	Start_ADC_Sampling();

	Ps2KeyValue = KeyValue_Null;

	
	while( Ps2KeyValue != KeyValue_Back )
	{		
		Delay_ms(10);
			
	}
	
	Change_Menu( 0 );
}

		

// 菜单4执行函数 参数：无
void MenuHaddler_4()
{
	Ps2KeyValue = KeyValue_Null;
	while( Ps2KeyValue != KeyValue_Back )
	{
		
	}
	
	Change_Menu( 0 );
}



/* ***************************** Custom Function Part       *****************************
 * 其他自定义函数区
 */

float User_Abs( float val )
{
	if( val >= 0 )
		return val;
	else
		return -val;
}


void AD9959_senddata(void)
{
		sendData(dds[0],0); 
			delay_ms(50);
	  sendData(dds[1],1);
	  	delay_ms(50);
	  sendData(dds[2],2); 
		delay_ms(50);
	  sendData(dds[3],3);  
		delay_ms(50);
}


void dds_show(void)
{
	if(dds[0].mode == NORMAL)
		{
			Show_Val( 1 , dds[0].range , "通道零：幅值%.0f   " );
			Show_Val( 11 , dds[0].fre , "频率%.0f   " );
			Show_Val( 2 , dds[0].phase , "        相位%.0f   " );
		}
		else
		{
			Show_Val( 1 , dds[0].fre_start , "通道零：起始%.0f   " );
			Show_Val( 11 , dds[0].fre_stop , "终止%.0f   " );
			Show_Val( 2 , dds[0].step_time , "        时间%.0f   " );
		}
		if(dds[1].mode == NORMAL)
		{
			Show_Val( 3 , dds[1].range , "通道一：幅值%.0f   " );
			Show_Val( 13 , dds[1].fre , "频率%.0f   " );
			Show_Val( 4 , dds[1].phase , "        相位%.0f   " );
		}
		else
		{
			Show_Val( 3 , dds[1].fre_start , "通道一：起始%.0f   " );
			Show_Val( 13 , dds[1].fre_stop , "终止%.0f   " );
			Show_Val( 4 , dds[1].step_time , "        时间%.0f   " );
		}
		if(dds[2].mode == NORMAL)
		{
			Show_Val( 5 , dds[2].range , "通道二：幅值%.0f   " );
			Show_Val( 15 , dds[2].fre , "频率%.0f   " );
			Show_Val( 6 , dds[2].phase , "        相位%.0f   " );
		}
		else
		{
			Show_Val( 5 , dds[2].fre_start , "通道二：起始%.0f   " );
			Show_Val( 15 , dds[2].fre_stop , "终止%.0f   " );
			Show_Val( 6 , dds[2].step_time , "        时间%.0f   " );
		}
		if(dds[3].mode == NORMAL)
		{
			Show_Val( 7 , dds[3].range , "通道三：幅值%.0f   " );
			Show_Val( 17 , dds[3].fre , "频率%.0f   " );
			Show_Val( 8 , dds[3].phase , "        相位%.0f   " );
		}
		else
		{
			Show_Val( 7 , dds[3].fre_start , "通道三：起始%.0f   " );
			Show_Val( 17 , dds[3].fre_stop , "终止%.0f   " );
			Show_Val( 8 , dds[3].step_time , "        时间%.0f   " );
		}
}

void DAC_setval(float val)
{
	uint16_t val_data;

	// 参数范围检查
    if(val < 0.0f) val = 0.0f;        // 限制最小值为0V
    if(val > 3.3f) val = 3.3f;       // 限制最大值为3.3V

	val_data = val / 3.3 * 0x0fff ;
	DAC_SetChannel1Data( DAC_Align_12b_R , val_data );
}

// 清除绘图区域 参数：1>起始x；2>起始y；3>结束x；4>结束y；5>背景色； 返回：无
void User_Data_PlotClear(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    // 参数有效性检查
    if (x1 >= 800 || x2 >= 800 || y1 >= 480 || y2 >= 480) {
        return; // 坐标超出屏幕范围
    }

    if (x1 > x2 || y1 > y2) {
        return; // 坐标顺序错误
    }

    // 使用LCD清除函数清除指定区域
    LCD_Appoint_Clear(x1, y1, x2, y2, color);
}

// 简化坐标轴绘制函数 参数：1>起始行；2>终止行；3>最小值；4>最大值；5>起始x；6>宽度；7>颜色；8>数据长度； 返回：无
void User_Data_PlotAxisSimple(uint8_t start_row, uint8_t end_row, float min_value, float max_value, uint16_t start_x, uint16_t width, uint16_t color, uint16_t data_length)
{
    uint16_t i; // 循环计数器
    uint16_t start_y, end_y; // 起始和终止y坐标
    uint16_t end_x; // 终止x坐标
    char label_str[10]; // 标签字符串
    float x_step; // x轴步长

    // 参数有效性检查
    if (start_row == 0 || end_row == 0 || start_row > 10 || end_row > 10) {
        return; // 行号范围无效
    }

    if (max_value <= min_value || width == 0 || data_length == 0) {
        return; // 参数无效
    }

    if (start_x + width >= 800) {
        return; // 超出屏幕范围
    }

    // 计算坐标轴的起始和终止位置
    start_y = 96 + 32 * (start_row - 1); // 起始行对应最大值
    end_y = 96 + 32 * (end_row - 1);     // 终止行对应最小值
    end_x = start_x + width;

    // 计算x轴步长（与绘图函数保持一致）
    x_step = (float)width / (float)(data_length - 1);

    // 绘制y轴（左侧垂直线）
    OS_Line_Draw(start_x, start_y, start_x, end_y, color);

    // 绘制x轴（底部水平线）
    OS_Line_Draw(start_x, end_y, end_x, end_y, color);

    // 绘制y轴刻度线和标签
    uint8_t y_ticks = (start_row > end_row) ? (start_row - end_row + 1) : (end_row - start_row + 1);
    for (i = 0; i < y_ticks; i++) {
        uint16_t tick_y;
        float tick_value;

        if (start_row > end_row) {
            // 起始行号大于终止行号
            tick_y = 96 + 32 * (start_row - 1 - i);
            tick_value = min_value + (max_value - min_value) * i / (y_ticks - 1);
        } else {
            // 起始行号小于终止行号
            tick_y = 96 + 32 * (start_row - 1 + i);
            tick_value = max_value - (max_value - min_value) * i / (y_ticks - 1);
        }

        // 绘制y轴刻度线
        OS_Line_Draw(start_x - 5, tick_y, start_x, tick_y, color);

        // 绘制y轴标签（每隔一个刻度显示）
        // if (i % 2 == 0) {
        //     sprintf(label_str, "%.0f", tick_value);
        //     OS_String_Show(start_x - 40, tick_y - 8, 16, 1, label_str);
        // }
    }

    // 绘制x轴刻度点（与数据点位置对应）
    uint16_t x_tick_interval = (data_length > 50) ? (data_length / 10) : 5; // 动态调整刻度间隔
    for (i = 0; i < data_length; i += x_tick_interval) {
        uint16_t tick_x = start_x + (uint16_t)(i * x_step);

        // 绘制x轴刻度点（短线）
        OS_Line_Draw(tick_x, end_y, tick_x, end_y + 3, color);
    }

    // 确保最后一个数据点也有刻度
    if ((data_length - 1) % x_tick_interval != 0) {
        uint16_t last_tick_x = start_x + (uint16_t)((data_length - 1) * x_step);
        OS_Line_Draw(last_tick_x, end_y, last_tick_x, end_y + 3, color);
    }

    // 绘制坐标轴标题
    OS_String_Show(start_x - 60, start_y + (end_y - start_y) / 2, 16, 1, "值");
    OS_String_Show(start_x + width / 2 - 20, end_y + 30, 16, 1, "频率");
}

// 绘制坐标轴 参数：1>起始行；2>终止行；3>最小值；4>最大值；5>起始x；6>宽度；7>颜色；8>x轴标值数组；9>x轴标值数量； 返回：无
void User_Data_PlotAxis(uint8_t start_row, uint8_t end_row, float min_value, float max_value, uint16_t start_x, uint16_t width, uint16_t color, float* x_labels, uint8_t x_label_count)
{
    uint16_t i; // 循环计数器
    uint16_t start_y, end_y; // 起始和终止y坐标
    uint16_t end_x; // 终止x坐标
    char label_str[10]; // 标签字符串

    // 参数有效性检查
    if (start_row == 0 || end_row == 0 || start_row > 10 || end_row > 10) {
        return; // 行号范围无效
    }

    if (max_value <= min_value || width == 0) {
        return; // 参数无效
    }

    if (start_x + width >= 800) {
        return; // 超出屏幕范围
    }

    if (x_labels == NULL || x_label_count == 0) {
        return; // x轴标值数组无效
    }

    // 计算坐标轴的起始和终止位置
    start_y = 96 + 32 * (start_row - 1); // 起始行对应最大值
    end_y = 96 + 32 * (end_row - 1);     // 终止行对应最小值
    end_x = start_x + width;

    // 绘制y轴（左侧垂直线）
    OS_Line_Draw(start_x, start_y, start_x, end_y, color);

    // 绘制x轴（底部水平线）
    OS_Line_Draw(start_x, end_y, end_x, end_y, color);

    // 绘制y轴刻度线和标签
    uint8_t y_ticks = (start_row > end_row) ? (start_row - end_row + 1) : (end_row - start_row + 1);
    for (i = 0; i < y_ticks; i++) {
        uint16_t tick_y;
        float tick_value;

        if (start_row > end_row) {
            // 起始行号大于终止行号
            tick_y = 96 + 32 * (start_row - 1 - i);
            tick_value = min_value + (max_value - min_value) * i / (y_ticks - 1);
        } else {
            // 起始行号小于终止行号
            tick_y = 96 + 32 * (start_row - 1 + i);
            tick_value = max_value - (max_value - min_value) * i / (y_ticks - 1);
        }

        // 绘制y轴刻度线
        OS_Line_Draw(start_x - 5, tick_y, start_x, tick_y, color);

        // 绘制y轴标签（每隔一个刻度显示）
        if (i % 2 == 0) {
            sprintf(label_str, "%.0f", tick_value);
            OS_String_Show(start_x - 40, tick_y - 8, 16, 1, label_str);
        }
    }

    // 绘制x轴刻度线（根据给定的标值数组）
    uint8_t x_ticks = (x_label_count > 8) ? 8 : x_label_count; // 最多显示8个刻度，避免过于拥挤
    for (i = 0; i < x_ticks; i++) {
        uint16_t tick_x;

        if (x_ticks == 1) {
            tick_x = start_x + width / 2; // 单个标签居中显示
        } else {
            tick_x = start_x + (width * i) / (x_ticks - 1); // 均匀分布
        }

        // 绘制x轴刻度线
        OS_Line_Draw(tick_x, end_y, tick_x, end_y + 5, color);

        // 绘制x轴标签（使用给定数组中的值）
        uint8_t label_index = (i * (x_label_count - 1)) / (x_ticks - 1); // 计算对应的标签索引
        if (label_index >= x_label_count) label_index = x_label_count - 1; // 边界保护

        sprintf(label_str, "%.1f", x_labels[label_index]);
        OS_String_Show(tick_x - 15, end_y + 10, 16, 1, label_str);
    }

    // 绘制坐标轴标题
    OS_String_Show(start_x - 60, start_y + (end_y - start_y) / 2, 16, 1, "值");
    OS_String_Show(start_x + width / 2 - 20, end_y + 30, 16, 1, "序号");
}


/**
 * @brief 数据映射函数 - 将数据值线性映射到LCD行坐标
 * @param data_value 要映射的数据值
 * @param start_row 起始行号(1-10)，对应数据最大值位置
 * @param end_row 终止行号(1-10)，对应数据零点位置
 * @param min_value 数据的最小值
 * @param max_value 数据的最大值
 * @return LCD行对应的y坐标像素值
 * @note 使用公式：y = 96 + 32 * (row - 1) 进行行号到像素坐标转换
 * @note 超出数据范围的值会被限制在边界坐标
 * @example User_Data_MapToRow(50.0f, 5, 7, 0.0f, 100.0f) 返回第6行坐标
 */
uint16_t User_Data_MapToRow(float data_value, uint8_t start_row, uint8_t end_row, float min_value, float max_value)
{
    uint16_t start_y, end_y; // 起始和终止y坐标
    float ratio; // 映射比例

    // 参数有效性检查
    if (start_row == 0 || end_row == 0 || start_row > 10 || end_row > 10) {
        return 96; // 返回默认第一行坐标
    }

    if (max_value <= min_value) {
        return 96; // 数据范围无效，返回默认坐标
    }

    // 计算起始和终止的y坐标
    start_y = 96 + 32 * (start_row - 1); // 起始行对应最大值
    end_y = 96 + 32 * (end_row - 1);     // 终止行对应最小值(零点位置)

    // 边界检查：数据值超出范围时返回边界坐标
    if (data_value <= min_value) {
        return end_y; // 返回终止行坐标(零点位置)
    }
    if (data_value >= max_value) {
        return start_y; // 返回起始行坐标
    }

    // 线性映射计算：y = end_y - (data - min) / (max - min) * (end_y - start_y)
    ratio = (data_value - min_value) / (max_value - min_value);

    // 返回映射后的y坐标(最大值在上方start_y，最小值在下方end_y)
    return end_y - (uint16_t)(ratio * (end_y - start_y));
}

/**
 * @brief 基础绘图函数 - 绘制数据数组的线条图
 * @param data_array 指向数据数组的指针，不能为NULL
 * @param data_length 数据数组长度，必须大于0
 * @param start_row 起始行号(1-10)，对应数据最大值显示位置
 * @param end_row 终止行号(1-10)，对应数据零点显示位置
 * @param min_value 数据的最小值，用于坐标映射
 * @param max_value 数据的最大值，用于坐标映射，必须大于min_value
 * @param start_x 绘图起始x坐标，建议在右侧显示区域(≥250)
 * @param color 绘图颜色，使用RGB565格式(如White、Red、Blue等)
 * @return 无
 * @note 自动连接相邻数据点形成连续曲线
 * @note 超出屏幕范围(x≥800)时自动停止绘制
 * @example User_Data_PlotDraw(data, 100, 1, 10, 0.0, 100.0, 300, White)
 */
// 缩放绘图函数 参数：1>数据数组；2>数据长度；3>起始行；4>终止行；5>最小值；6>最大值；7>起始x；8>绘图宽度；9>颜色；10>模式； 返回：无
void User_Data_PlotDrawScaled(float* data_array, uint16_t data_length, uint8_t start_row, uint8_t end_row, float min_value, float max_value, uint16_t start_x, uint16_t width, uint16_t color, User_PlotMode_t mode)
{
    uint16_t i; // 循环计数器
    uint16_t x1, y1, x2, y2; // 当前点和下一点的坐标
    float x_step; // x轴步长

    // 参数有效性检查
    if (data_array == NULL || data_length == 0 || width == 0) {
        return; // 参数无效
    }

    if (start_row == 0 || end_row == 0 || start_row > 10 || end_row > 10) {
        return; // 行号范围无效
    }

    if (max_value <= min_value) {
        return; // 数据范围无效
    }

    if (start_x + width >= 800) {
        return; // 超出屏幕范围
    }

    // 计算x轴步长
    x_step = (float)width / (float)(data_length - 1);

    // 根据绘图模式选择绘制方式
    switch (mode) {
        case USER_PLOT_LINE:
            // 线条模式：连接相邻数据点
            for (i = 0; i < data_length - 1; i++) {
                // 计算当前点坐标
                x1 = start_x + (uint16_t)(i * x_step);
                y1 = User_Data_MapToRow(data_array[i], start_row, end_row, min_value, max_value);

                // 计算下一点坐标
                x2 = start_x + (uint16_t)((i + 1) * x_step);
                y2 = User_Data_MapToRow(data_array[i + 1], start_row, end_row, min_value, max_value);

                // 使用底层绘图函数连接相邻数据点
                OS_Line_Draw(x1, y1, x2, y2, color);
            }
            break;

        case USER_PLOT_POINT:
            // 点模式：绘制离散数据点
            for (i = 0; i < data_length; i++) {
                // 计算当前点坐标
                x1 = start_x + (uint16_t)(i * x_step);
                y1 = User_Data_MapToRow(data_array[i], start_row, end_row, min_value, max_value);

                // 绘制数据点
                OS_Point_Draw(x1, y1, color);
            }
            break;

        case USER_PLOT_BAR:
            // 柱状图模式：绘制垂直柱状图
            for (i = 0; i < data_length; i++) {
                // 计算当前点坐标
                x1 = start_x + (uint16_t)(i * x_step);
                y1 = User_Data_MapToRow(data_array[i], start_row, end_row, min_value, max_value);

                // 计算零点位置（终止行对应零值）
                uint16_t zero_y = 96 + 32 * (end_row - 1);

                // 绘制从零点到数据点的垂直线（柱状图）
                OS_Line_Draw(x1, zero_y, x1, y1, color);
            }
            break;

        default:
            // 默认使用线条模式
            User_Data_PlotDrawScaled(data_array, data_length, start_row, end_row, min_value, max_value, start_x, width, color, USER_PLOT_LINE);
            break;
    }
}


// 修正相位范围(FFT直接做相位差得到范围为-360~360)
float User_FixPhase( float pha )
{
	while( 1 )
	{
		if( pha < -180 )
			pha = pha + 360;
		else if( pha > 180 )
			pha = pha - 360;
		else
			return pha;
	}
}


// 获取信号幅频相频 参数：1>采样电压数据(返回幅频数据)；2>返回相频数据； 返回：1>基波频点下标
uint16_t Get_FreSpectrum( float vol_data[ADCDataLength] , float pha_data[ADCDataLength] )
{
	uint16_t count , index = 0;		// 计数器；基波频点下标
	complex data[ADCDataLength];	// 复数数据组
	float vpp_max = 0;						// 最大幅值
	
	// 将电压数据转化为复数
	for( count = 0 ; count < ADCDataLength ; count ++ )	
	{
		data[count].real = vol_data[count];	// 实部为电压值
		data[count].imag = 0;								// 虚部为0
	}
	
	// 对数据做FFT
	//fft( ADCDataLength , data );												
	fft_process(data);
	// 对前半数据取模以及求出相角
	for( count = 0 ; count < ADCDataLength / 2 ; count ++ )	
	{
		vol_data[count] = sqrt(data[count].real * data[count].real + data[count].imag * data[count].imag);
		pha_data[count] = atan2( data[count].imag , data[count].real ) * 180 / PI;
	}
	
	// 提取直流量
	vol_data[0] = vol_data[0] / ADCDataLength;	// 第一个数据除以采样点数为直流分量
	
	// 提取谐波分量
	for( count = 1 ; count < ADCDataLength / 2 ; count ++ )
		vol_data[count] = vol_data[count] / ADCDataLength * 4;	// 对应频点模值除以采样点数乘以4得到幅值
	
	// 寻找基波频点(除直流外幅值最大的频率点)
	for( count = 1 ; count < ADCDataLength / 2 ; count ++ )
	{
		if( vol_data[count] > vpp_max )
		{
			vpp_max = vol_data[count];
			index = count;
		}
	}
	
	return index;
}

void meanFilter(float* input, float* output, uint16_t length, uint8_t windowSize)
{
    // 参数检查
    if (input == NULL || output == NULL || length == 0) {
        return ;
    }
    
    // 窗口大小必须小于数组长度
    if (windowSize > length) {
        return ;
    }
    
    // 计算半窗口大小(左侧)
    uint8_t halfWindowLeft = windowSize / 2;
    
    // 计算半窗口大小(右侧) - 处理偶数窗口的情况
    uint8_t halfWindowRight = (windowSize % 2 == 0) ? (windowSize / 2 - 1) : (windowSize / 2);
    
    // 处理边缘情况：开头的元素
    for (uint16_t i = 0; i < halfWindowLeft; i++) {
        float sum = 0.0f;
        uint8_t count = 0;
        
        // 只能使用当前点及其后面的点
        for (uint16_t j = 0; j <= i + halfWindowRight; j++) {
            sum += input[j];
            count++;
        }
        
        output[i] = sum / count;
    }
    
    // 处理中间的元素
    for (uint16_t i = halfWindowLeft; i < length - halfWindowRight; i++) {
        float sum = 0.0f;
        
        // 使用完整窗口
        for (int16_t j = i - halfWindowLeft; j <= i + halfWindowRight; j++) {
            sum += input[j];
        }
        
        output[i] = sum / windowSize;
    }
    
    // 处理边缘情况：结尾的元素
    for (uint16_t i = length - halfWindowRight; i < length; i++) {
        float sum = 0.0f;
        uint8_t count = 0;
        
        // 只能使用当前点及其前面的点
        for (uint16_t j = i - halfWindowLeft; j < length; j++) {
            sum += input[j];
            count++;
        }
        
        output[i] = sum / count;
    }
    
}

// 获取信号幅值和相位
void User_GetSignalInf( float vpp[2] , float *pha_dif )
{
	uint16_t index = 0;
	uint32_t count;
	float hanningCoef;
	float vol1[ADCDataLength] = {0} , vol2[ADCDataLength] = {0};
	float t_pha[ADCDataLength] = {0};
	float temp1[ADCDataLength] = {0};//采集的是temp数组中，然后进行均值滤波
	float temp2[ADCDataLength] = {0};


	// 获取双通道电压数据
	//Get_ACVol( temp1 , temp2 );//ADC采样获取数据
	Get_ACVol( vol1 , vol2 );//ADC采样获取数据
	
	//进行四点均值滤波，并将滤波得到的数据放到vol数组中
	//meanFilter(temp1 , vol1 , ADCDataLength,windowsize);
	//meanFilter(temp2 , vol2 , ADCDataLength,windowsize);
	
	// for( count = 0 ; count < ADCDataLength ; count ++ )
	// {
	// 	hanningCoef = 0.5 * (1.0 - cosf(2.0*PI*count/(ADCDataLength-1)));
	// 	// 低16位为ADC1的数据
	// 	vol1[count] *= hanningCoef;
	// 	// 高16位为ADC2的数据
	// 	vol2[count] *= hanningCoef;
	// }
	for( count = 0 ; count < ADCDataLength/8 ; count ++ )
	{
	WaveData[count] = (uint16_t)(vol1[8*count]*5 / 3.3 * 0x0fff + 0.5f);
	}

	// for( count = 0 ; count < ADCDataLength ; count ++ )
	// {
	// 	printf("%d\n",vol1[count]);
	// }
	//TIM_Cmd(TIM6 , ENABLE);
	// 获取通道一输入信号的基波频点幅值和相位
	index = Get_FreSpectrum( vol1 , t_pha );
	vpp[0] = vol1[index];
	*pha_dif = t_pha[index];
	// for( count = 0 ; count < ADCDataLength/8 ; count ++ )
	// {
	// WaveData[count] = (uint16_t)(vol1[8*count] / 3.3 * 0x0fff + 0.5f);
	// }
	// 通道二
	index = Get_FreSpectrum( vol2 , t_pha );
	vpp[1] = vol2[index];
	*pha_dif = *pha_dif - t_pha[index];		// 直接做相位差
	
	// 修正范围
	*pha_dif = User_FixPhase( *pha_dif );
}


//从1256读电压函数 参数：1>通道1；2>通道2；3>模式(0为单通道测量，1为两通道差分测量) 返回：电压值( -5v 至 +5v )
float ADS1256_ReadVol( uint8_t ch_1 , uint8_t ch_2 , uint8_t mode ) 
{
	float adc_data;
	float vol;
	float base_vol = 4.9896;;
	
	if( ch_1 < 8 )
		ch_1 = ch_1 * 16;
	else 
		ch_1 = 0;
	
	if( mode == 0 )
		ch_2 = ADS1256_MUXN_AINCOM;
	
	adc_data = ADS1256ReadData( ch_1 | ch_2 );
	
	adc_data = adc_data > 0x7fffff ? adc_data - 0xffffff : adc_data;
	
	vol = (float)adc_data / 0x7fffff * base_vol;
	
	return vol;
}




//从PS2输入数字 参数：1>未按键时返回的数值 返回：键入的数值
float PS2_ReadNum( float num )
{
	uint8_t count = 0;
	uint8_t dec_sign = 0;
	float temp_num = 0;
	u8 minus_flag = 0;
	
	if( Ps2KeyValue <= KeyValue_9 )
	{
		TIM_Cmd( TIM6 , DISABLE );
		

		LCD_Appoint_Clear( 332 , 96 + 64 * 4 , 750 + 1 , 480 - 32 - 8 , Black );
		OS_Rect_Draw( 332 , 96 + 64 * 4 , 750 , 96 + 64 * 5 , 1 , White );
		
		while( Ps2KeyValue <= KeyValue_Point || Ps2KeyValue == KeyValue_Minus)
		{
			if(Ps2KeyValue == KeyValue_Minus)
				minus_flag = 1;
			if( dec_sign == 0 )
			{
				if( Ps2KeyValue == KeyValue_Point )
				{
					dec_sign = 1;
					count = 0;
				}
				else
				{
					if(Ps2KeyValue == KeyValue_Minus)
						temp_num = -temp_num;
					else
					temp_num = temp_num * 10 + Ps2KeyValue;
				}		
			}
			else
				temp_num = temp_num + (float)Ps2KeyValue / pow( 10 , count );
			
			OS_Num_Show( 332 + 16 , 96 + 64 * 4 + 16 , 32 , 1 ,temp_num , "-> %.2f" );
			
			count ++;
			Ps2KeyValue = KeyValue_Null;
			while( Ps2KeyValue == KeyValue_Null );
		}
		
		Ps2KeyValue = KeyValue_Null;
		LCD_Appoint_Clear( 332 , 96 + 64 * 4 , 750 + 1 , 480 - 32 - 8 , Black );
	}
	else
		temp_num = num;
	
	Ps2KeyValue = KeyValue_Null;
	
	 if(minus_flag == 1)
	 	temp_num = -temp_num;
	return temp_num;
}











/* ***************************** IRQHandler Part    	     	*****************************
 * 中断执行函数区
 */


/* ***************************** 						END 	   	     	*****************************/










// float Calculate_Smooth_Impedance(float raw_z, uint8_t current_gear) {
//     float result = raw_z;
    
//     // 1.3k和36k档位的重叠区域处理
//     if(current_gear == Gear_1200 && raw_z > 1800 && raw_z < 2000) {
//         // 在1800-2000欧姆的重叠区域
//         float weight = (raw_z - 1800) / 200.0f;  // 0到1的权重
        
//         // 临时切换到高档位进行测量
//         uint8_t original_gear = Gear_sign;
//         User_SetGear(Gear_15k);
        
//         // 获取高档位的测量值
//         float vpp_high[2];
//         float pha_dif_high;
//         User_GetSignalInf(vpp_high, &pha_dif_high);
//         float z_high = Rref_15k * vpp_high[0] / vpp_high[1];
        
//         // 恢复原档位
//         User_SetGear(original_gear);
        
//         // 加权平均
//         result = raw_z * (1 - weight) + z_high * weight;
//     }
    
//     // ... 类似处理其他重叠区域
    
//     return result;
// }


// float Calibrate_Impedance(float raw_z, uint8_t gear) {
//     float calibrated_z = raw_z;
    
//     switch(gear) {
//         case Gear_51:
//             // 51欧姆档位校准曲线
//             calibrated_z = raw_z * 1.05;  // 简单示例，实际需测量多点确定
//             break;
            
//         case Gear_1200:
//             // 1300欧姆档位分段校准
//             if(raw_z >= 2400 && raw_z <= 2700)
//                 calibrated_z = raw_z * 1.35;
//             else if(raw_z > 2700 && raw_z <= 3000)
//                 calibrated_z = raw_z * (1.35 + (raw_z-2700)/(3000-2700)*(1.40-1.35));
//             else if(raw_z > 3000 && raw_z <= 3200)
//                 calibrated_z = raw_z * 1.40;
//             else if(raw_z > 3200 && raw_z <= 8500)
//                 calibrated_z = raw_z * (1.40 + (raw_z-3200)/(8500-3200)*(0.55-1.40));
//             break;
            
//         case Gear_15k:
//             // 36k欧姆档位分段校准
//             if(raw_z >= 8500 && raw_z <= 11800)
//                 calibrated_z = raw_z * 0.55;
//             // ... 其他分段
//             break;
            
//         case Gear_750k:
//             // 1000k欧姆档位校准
//             calibrated_z = raw_z * 0.95;  // 示例值
//             break;
//     }
    
//     return calibrated_z;
// }

